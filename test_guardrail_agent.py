#!/usr/bin/env python3
"""
Test runner for the AI-powered GuardrailAgent (No Hardcoding).

This script tests the completely AI-driven GuardrailAgent using sample outputs
from the test_data directory. The agent uses pure AI reasoning without hardcoded logic.
"""

import json
import os
from pathlib import Path
from agents.guardrail_agent import GuardrailAgent

def load_test_data(filename: str) -> dict:
    """Load test data from the test_data directory"""
    filepath = Path('test_data') / filename
    with open(filepath, 'r', encoding='utf-8') as f:
        return json.load(f)

def test_file_validation_agent():
    """Test auditing FileValidationAgent output"""
    print("\n" + "="*60)
    print("TESTING: FileValidationAgent Audit")
    print("="*60)
    
    # Load test data
    agent_output = load_test_data('file_validation_output.json')
    
    # Initialize GuardrailAgent
    guardrail = GuardrailAgent()
    
    # Run audit
    result = guardrail.audit_agent_output(
        agent_name="FileValidationAgent",
        agent_phase="Audit Phase 1",
        agent_output=agent_output
    )
    
    # Display results
    print(f"Agent: {result['agent']}")
    print(f"Phase: {result['phase']}")
    print(f"Status: {result['status']}")
    print(f"Findings ({len(result['findings'])}):")
    for i, finding in enumerate(result['findings'], 1):
        print(f"  {i}. {finding}")
    print(f"Escalation: {result['escalation']}")
    
    return result

def test_data_validation_agent():
    """Test auditing DataValidationAgent output"""
    print("\n" + "="*60)
    print("TESTING: DataValidationAgent Audit")
    print("="*60)
    
    # Load test data
    agent_output = load_test_data('data_validation_output.json')
    
    # Initialize GuardrailAgent
    guardrail = GuardrailAgent()
    
    # Run audit
    result = guardrail.audit_agent_output(
        agent_name="DataValidationAgent",
        agent_phase="Audit Phase 2",
        agent_output=agent_output
    )
    
    # Display results
    print(f"Agent: {result['agent']}")
    print(f"Phase: {result['phase']}")
    print(f"Status: {result['status']}")
    print(f"Findings ({len(result['findings'])}):")
    for i, finding in enumerate(result['findings'], 1):
        print(f"  {i}. {finding}")
    print(f"Escalation: {result['escalation']}")
    
    return result



def test_error_handling():
    """Test error handling with invalid input"""
    print("\n" + "="*60)
    print("TESTING: Error Handling")
    print("="*60)
    
    # Initialize GuardrailAgent
    guardrail = GuardrailAgent()
    
    # Test with invalid agent name
    result = guardrail.audit_agent_output(
        agent_name="InvalidAgent",
        agent_phase="Audit Phase 1",
        agent_output={"test": "data"}
    )
    
    print(f"Invalid agent test - Status: {result['status']}")
    print(f"Findings: {result['findings']}")
    
    return result

def main():
    """Run all tests"""
    print("GuardrailAgent Test Suite")
    print("=" * 60)
    
    # Check if OpenAI API key is available
    if not os.getenv('OPENAI_API_KEY'):
        print("ERROR: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key to run the tests")
        return
    
    try:
        # Run tests
        results = []
        
        results.append(test_file_validation_agent())
        results.append(test_data_validation_agent())
        results.append(test_error_handling())
        
        # Summary
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        
        for i, result in enumerate(results, 1):
            agent = result.get('agent', 'Unknown')
            status = result.get('status', 'Unknown')
            findings_count = len(result.get('findings', []))
            escalation = result.get('escalation', 'Unknown')
            
            print(f"Test {i}: {agent} - {status} ({findings_count} findings) - Escalation: {escalation}")
        
        print(f"\nLogs written to: data/audit_logs/")
        print(f"Reports written to: data/reports/")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
