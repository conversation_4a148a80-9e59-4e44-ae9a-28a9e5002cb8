import openai
import yaml
import json
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
from typing import Dict, Any, Optional, List
from pathlib import Path
from pydantic import ValidationError

# Import Pydantic models for structured validation
from .models import (
    AuditReport, AuditLogEntry, AuditStatus, EscalationStatus,
    FileValidationOutput, DataValidationOutput, AIAnalysisResult,
    GuardrailConfig, AgentProfile, ThresholdConfig
)

# Load environment variables from .env file
load_dotenv()

class GuardrailAgent:
    """
    AI-powered reasoning-based audit agent for validating outputs from autonomous validation agents.

    This agent is purely AI-driven with minimal hardcoding. It uses structured prompts and
    configuration-driven logic to audit agent outputs without redoing their work.
    """

    def __init__(self, api_key: Optional[str] = None):
        # Use provided API key or load from environment
        if api_key is None:
            api_key = os.getenv('OPENAI_API_KEY')

        if not api_key:
            raise ValueError("OpenAI API key must be provided either as parameter or OPENAI_API_KEY environment variable")

        self.client = openai.OpenAI(api_key=api_key)

        # Get model from environment or use default
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
        self.temperature = float(os.getenv('AI_TEMPERATURE', '0.1'))
        self.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        self.timeout_seconds = int(os.getenv('AUDIT_TIMEOUT_SECONDS', '30'))

        # Load configurations and prompts
        self._load_configurations()
        self._load_prompt_templates()

        # Ensure logging directories exist
        self.audit_logs_dir = Path('data/audit_logs')
        self.reports_dir = Path('data/reports')
        self.audit_logs_dir.mkdir(parents=True, exist_ok=True)
        self.reports_dir.mkdir(parents=True, exist_ok=True)

        # Initialize log file paths
        self.json_log_file = self.audit_logs_dir / 'audit_logs.jsonl'
        self.human_log_file = self.audit_logs_dir / 'audit_human_readable.log'

    def _load_configurations(self):
        """Load YAML configuration files with proper error handling"""
        try:
            # Ensure agent_profiles.yaml exists and is up-to-date
            self._ensure_agent_profiles()

            # Load agent profiles with validation
            profiles_path = Path('config/agent_profiles.yaml')
            if not profiles_path.exists():
                raise FileNotFoundError(f"Agent profiles file not found: {profiles_path}")

            with open(profiles_path, 'r', encoding='utf-8') as f:
                self.agent_profiles = yaml.safe_load(f)

            if not self.agent_profiles:
                raise ValueError("Agent profiles file is empty or invalid")

            # Load thresholds with validation
            thresholds_path = Path('config/thresholds.yaml')
            if not thresholds_path.exists():
                raise FileNotFoundError(f"Thresholds file not found: {thresholds_path}")

            with open(thresholds_path, 'r', encoding='utf-8') as f:
                self.thresholds = yaml.safe_load(f)

            if not self.thresholds:
                raise ValueError("Thresholds file is empty or invalid")

            print(f"✅ Loaded configurations: {len(self.agent_profiles)} agent profiles, {len(self.thresholds)} threshold categories")

        except Exception as e:
            print(f"❌ Error loading configurations: {e}")
            # Set minimal fallback configurations
            self.agent_profiles = {}
            self.thresholds = {
                'escalation_thresholds': {'critical_issues': 1, 'warning_issues': 3},
                'performance_thresholds': {},
                'schema_thresholds': {'missing_required_fields': 0}
            }
            print("⚠️  Using minimal fallback configurations")

    def _load_prompt_templates(self):
        """Load prompt templates from ContextGuardrail directory"""
        context_dir = Path('ContextGuardrail')

        with open(context_dir / 'base_instruction_prompt.txt', 'r', encoding='utf-8') as f:
            self.base_instruction = f.read()
        with open(context_dir / 'schema_violation_prompt.txt', 'r', encoding='utf-8') as f:
            self.schema_violation_template = f.read()
        with open(context_dir / 'hallucination_check_prompt.txt', 'r', encoding='utf-8') as f:
            self.hallucination_template = f.read()
        with open(context_dir / 'escalation_policy_prompt.txt', 'r', encoding='utf-8') as f:
            self.escalation_template = f.read()

    def _ensure_agent_profiles(self):
        """Ensure agent_profiles.yaml exists and is up-to-date with SP_Parser_Agent outputs"""
        profiles_path = Path('config/agent_profiles.yaml')
        schema_path = Path('test_data/schema_template.json')
        rules_path = Path('test_data/data_rules.json')

        # Check if we need to generate/regenerate profiles
        should_generate = (
            not profiles_path.exists() or
            (schema_path.exists() and rules_path.exists() and
             profiles_path.stat().st_mtime < max(schema_path.stat().st_mtime, rules_path.stat().st_mtime))
        )

        if should_generate and schema_path.exists() and rules_path.exists():
            print("🔄 Generating agent profiles from SP_Parser_Agent outputs...")
            self._generate_agent_profiles()
        elif not profiles_path.exists():
            print("⚠️  No agent_profiles.yaml found and no SP_Parser_Agent outputs available.")
            print("   Creating minimal default profiles...")
            self._create_default_profiles()

    def _generate_agent_profiles(self):
        """Generate agent profiles dynamically using AI analysis of SP_Parser_Agent outputs"""
        try:
            # Load SP_Parser_Agent outputs
            with open('test_data/schema_template.json', 'r') as f:
                schema_template = json.load(f)
            with open('test_data/data_rules.json', 'r') as f:
                data_rules = json.load(f)

            # Use AI to generate profiles based on the data
            ai_prompt = f"""
            Based on the following SP_Parser_Agent outputs, generate agent profiles for FileValidationAgent and DataValidationAgent.

            Schema Template:
            {json.dumps(schema_template, indent=2)}

            Data Rules:
            {json.dumps(data_rules, indent=2)}

            Generate a YAML configuration that defines:
            1. Capabilities and limitations for each agent
            2. Expected output structures based on what these agents would logically produce
            3. Performance thresholds

            Return as valid YAML format for agent_profiles.yaml
            """

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert at analyzing data schemas and generating agent configurations. Generate practical, realistic agent profiles."},
                    {"role": "user", "content": ai_prompt}
                ],
                temperature=0.1
            )

            # Parse AI-generated YAML
            ai_yaml = response.choices[0].message.content.strip()
            # Extract YAML from response if wrapped in markdown
            if '```yaml' in ai_yaml:
                ai_yaml = ai_yaml.split('```yaml')[1].split('```')[0].strip()
            elif '```' in ai_yaml:
                ai_yaml = ai_yaml.split('```')[1].split('```')[0].strip()

            agent_profiles = yaml.safe_load(ai_yaml)

            # Write to file
            Path('config').mkdir(exist_ok=True)
            with open('config/agent_profiles.yaml', 'w') as f:
                yaml.dump(agent_profiles, f, default_flow_style=False, indent=2, sort_keys=False)

            print(f"✅ AI-generated agent_profiles.yaml with {len(agent_profiles)} agent types")

        except Exception as e:
            print(f"❌ Error generating AI-based agent profiles: {e}")
            self._create_minimal_profiles()

    def _create_minimal_profiles(self):
        """Create minimal profiles when AI generation fails"""
        minimal_profiles = {
            'FileValidationAgent': {
                'capabilities': ['File validation', 'Schema checking'],
                'limitations': ['Cannot validate business logic'],
                'expected_output_structure': 'AI will analyze actual output format'
            },
            'DataValidationAgent': {
                'capabilities': ['Data validation', 'Business rule checking'],
                'limitations': ['Cannot modify source data'],
                'expected_output_structure': 'AI will analyze actual output format'
            }
        }

        Path('config').mkdir(exist_ok=True)
        with open('config/agent_profiles.yaml', 'w') as f:
            yaml.dump(minimal_profiles, f, default_flow_style=False, indent=2, sort_keys=False)

        print("✅ Created minimal AI-driven agent_profiles.yaml")

    def audit_agent_output(self, agent_name: str, agent_phase: str, agent_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main audit method - AI-powered with minimal hardcoding.

        Uses AI to analyze agent output against configurations and determine findings.
        """
        start_time = datetime.now(timezone.utc)

        # Log audit start
        self._log_audit_start(agent_name, agent_phase)

        try:
            # Validate and parse agent output based on agent type
            parsed_output = self._parse_agent_output(agent_name, agent_output)

            # Get agent configuration
            agent_config = self.agent_profiles.get(agent_name, {})
            if not agent_config:
                return self._create_error_report(
                    agent_name, agent_phase,
                    f"No configuration found for agent: {agent_name}",
                    start_time
                )

            # Create structured audit prompt using templates
            schema_check_prompt = self.schema_violation_template.format(
                agent_name=agent_name,
                expected_schema=json.dumps(agent_config.get('expected_output_structure', {}), indent=2),
                agent_output=json.dumps(agent_output, indent=2)
            )

            hallucination_check_prompt = self.hallucination_template.format(
                agent_name=agent_name,
                agent_output=json.dumps(agent_output, indent=2),
                agent_capabilities=json.dumps(agent_config.get('capabilities', []), indent=2)
            )

            escalation_check_prompt = self.escalation_template.format(
                agent_name=agent_name,
                agent_phase=agent_phase,
                findings="[To be determined after analysis]",
                escalation_thresholds=json.dumps(self.thresholds.get('escalation_thresholds', {}), indent=2)
            )

            # Create completely AI-driven audit prompt
            audit_prompt = f"""
            You are auditing the output of {agent_name} in {agent_phase}.

            AGENT CONFIGURATION (use this to understand capabilities and limitations):
            {json.dumps(agent_config, indent=2)}

            THRESHOLDS (use these for determining severity):
            {json.dumps(self.thresholds, indent=2)}

            AGENT OUTPUT TO AUDIT:
            {json.dumps(agent_output, indent=2)}

            STRUCTURED PROMPTS FOR GUIDANCE:

            Schema Check: {schema_check_prompt}

            Hallucination Check: {hallucination_check_prompt}

            Escalation Assessment: {escalation_check_prompt}

            Using your AI reasoning, analyze this agent output completely. Consider:
            - Does the output structure make sense for this agent type?
            - Are there any logical inconsistencies or impossible values?
            - Does the output align with the agent's stated capabilities?
            - Are there any patterns that suggest errors or hallucinations?
            - Based on the thresholds, what severity level is appropriate?
            - Should this be escalated to humans?

            Provide your analysis in this exact JSON format:
            {{
                "findings": ["finding 1", "finding 2", ...],
                "status": "PASSED" | "WARNING" | "CRITICAL",
                "escalation": "RECOMMENDED" | "NOT_NEEDED"
            }}

            Use your reasoning to determine everything - no hardcoded rules.
            """

            # Get AI analysis with retry logic
            ai_result = self._get_ai_analysis(audit_prompt)

            # Calculate processing time
            processing_time_ms = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)

            # Create final report using Pydantic model
            report = AuditReport(
                agent=agent_name,
                phase=agent_phase,
                status=AuditStatus(ai_result.status),
                findings=ai_result.findings,
                escalation=EscalationStatus(ai_result.escalation),
                processing_time_ms=processing_time_ms
            )

            # Log the audit
            self._log_audit(report)

            return report.model_dump()

        except Exception as e:
            return self._create_error_report(agent_name, agent_phase, str(e), start_time)

    def _parse_agent_output(self, agent_name: str, agent_output: Dict[str, Any]) -> Dict[str, Any]:
        """Parse agent output - purely pass-through, let AI handle all validation"""
        # No hardcoded parsing - AI will analyze the raw output against configurations
        return agent_output

    def _get_ai_analysis(self, audit_prompt: str) -> AIAnalysisResult:
        """Get AI analysis - purely AI-driven with minimal intervention"""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": self.base_instruction},
                        {"role": "user", "content": audit_prompt}
                    ],
                    temperature=self.temperature,
                    timeout=self.timeout_seconds
                )

                # Parse AI response - let AI handle all logic
                response_content = response.choices[0].message.content.strip()
                ai_data = self._extract_json_from_response(response_content)

                # Process AI findings - ensure they are strings
                findings = ai_data.get("findings", ["AI provided no specific findings"])
                if isinstance(findings, list):
                    # Convert any non-string findings to strings
                    findings = [str(f) if not isinstance(f, str) else f for f in findings]
                else:
                    findings = [str(findings)]

                # Minimal validation - let AI determine everything
                return AIAnalysisResult(
                    findings=findings,
                    status=AuditStatus(ai_data.get("status", "WARNING")),
                    escalation=EscalationStatus(ai_data.get("escalation", "NOT_NEEDED"))
                )

            except Exception as e:
                last_error = e
                self._log_warning(f"AI analysis attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    continue

        # Minimal fallback - let AI decide in next attempt or escalate
        return AIAnalysisResult(
            findings=[f"Technical issue prevented AI analysis: {last_error}"],
            status=AuditStatus.CRITICAL,
            escalation=EscalationStatus.RECOMMENDED
        )

    def _extract_json_from_response(self, response_content: str) -> Dict[str, Any]:
        """Extract JSON from AI response with flexible parsing"""
        try:
            return json.loads(response_content)
        except json.JSONDecodeError:
            # Try to find JSON within the response
            import re
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass

            # Minimal fallback - let AI reasoning show through
            return {
                "findings": [f"AI response format issue - Raw response: {response_content[:300]}"],
                "status": "WARNING",
                "escalation": "NOT_NEEDED"
            }

    def _create_error_report(self, agent_name: str, agent_phase: str, error_message: str, start_time: datetime) -> Dict[str, Any]:
        """Create an error report when audit fails"""
        processing_time_ms = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)

        report = AuditReport(
            agent=agent_name,
            phase=agent_phase,
            status=AuditStatus.CRITICAL,
            findings=[f"Audit error: {error_message}"],
            escalation=EscalationStatus.RECOMMENDED,
            processing_time_ms=processing_time_ms
        )

        # Log the error report
        self._log_audit(report)
        return report.model_dump()

    def _log_audit_start(self, agent_name: str, agent_phase: str):
        """Log the start of an audit"""
        timestamp = datetime.now(timezone.utc).isoformat()
        human_log = f"{timestamp} [START] Audit starting - {agent_name} {agent_phase}"

        try:
            with open(self.human_log_file, 'a', encoding='utf-8') as f:
                f.write(human_log + '\n')
        except Exception as e:
            print(f"Warning: Could not write to human log file: {e}")

    def _log_warning(self, message: str):
        """Log a warning message"""
        timestamp = datetime.now(timezone.utc).isoformat()
        human_log = f"{timestamp} [WARNING] {message}"

        try:
            with open(self.human_log_file, 'a', encoding='utf-8') as f:
                f.write(human_log + '\n')
        except Exception as e:
            print(f"Warning: Could not write to human log file: {e}")

    def _log_audit(self, report: AuditReport):
        """Enhanced dual logging for audit results following user preferences"""
        timestamp = datetime.now(timezone.utc).isoformat()

        try:
            # Create structured JSON log entry
            status_str = report.status.value if hasattr(report.status, 'value') else str(report.status)
            escalation_str = report.escalation.value if hasattr(report.escalation, 'value') else str(report.escalation)

            json_log_entry = AuditLogEntry(
                timestamp=timestamp,
                type="audit_complete",
                agent=report.agent,
                phase=report.phase,
                status=status_str,
                findings_count=len(report.findings),
                escalation=escalation_str,
                processing_time_ms=report.processing_time_ms
            )

            # Create human-readable log with status markers (following user preferences)
            if report.status == AuditStatus.PASSED:
                status_marker = "[OK]"
            elif report.escalation == EscalationStatus.RECOMMENDED:
                status_marker = "[ESCALATE]"
            else:
                status_marker = "[DONE]"

            # Handle enum values properly
            status_str = report.status.value if hasattr(report.status, 'value') else str(report.status)
            human_log = f"{timestamp} {status_marker} Audit complete - {report.agent} {status_str} ({len(report.findings)} findings)"

            # Write structured JSON log (for traceability)
            try:
                with open(self.json_log_file, 'a', encoding='utf-8') as f:
                    f.write(json_log_entry.model_dump_json(exclude_none=True).decode('utf-8') + '\n')
            except Exception as e:
                print(f"Error writing JSON log: {e}")

            # Write human-readable log (manager/demo-friendly formatting)
            try:
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(human_log + '\n')
            except Exception as e:
                print(f"Error writing human log: {e}")

            # Write detailed audit report
            safe_timestamp = timestamp.replace(':', '-').replace('.', '-')
            report_filename = self.reports_dir / f"audit_report_{report.agent}_{safe_timestamp}.json"

            try:
                with open(report_filename, 'w', encoding='utf-8') as f:
                    # Convert to dict for JSON serialization
                    report_dict = report.model_dump()
                    # Ensure datetime is serialized properly
                    if 'audit_timestamp' in report_dict and report_dict['audit_timestamp']:
                        report_dict['audit_timestamp'] = report_dict['audit_timestamp'].isoformat()
                    json.dump(report_dict, f, indent=2, ensure_ascii=False)

                # Log successful report creation
                success_log = f"{timestamp} [STEP] Report saved - {report_filename.name}"
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(success_log + '\n')

            except Exception as e:
                error_log = f"{timestamp} [ERROR] Failed to save report: {e}"
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(error_log + '\n')
                print(f"Error writing audit report: {e}")

        except Exception as e:
            # Fallback logging if structured logging fails
            fallback_log = f"{timestamp} [ERROR] Logging failed for {report.agent}: {e}"
            try:
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(fallback_log + '\n')
            except:
                print(fallback_log)





