import openai
import yaml
import json
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
from typing import Dict, Any, Optional, List
from pathlib import Path

# Load environment variables from .env file
load_dotenv()

class GuardrailAgent:
    """
    AI-powered reasoning-based audit agent for validating outputs from autonomous validation agents.

    This agent is purely AI-driven with minimal hardcoding. It uses structured prompts and
    configuration-driven logic to audit agent outputs without redoing their work.
    """

    def __init__(self, api_key: Optional[str] = None):
        # Use provided API key or load from environment
        if api_key is None:
            api_key = os.getenv('OPENAI_API_KEY')

        if not api_key:
            raise ValueError("OpenAI API key must be provided either as parameter or OPENAI_API_KEY environment variable")

        self.client = openai.OpenAI(api_key=api_key)

        # Get model from environment or use default
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')

        # Load configurations and prompts
        self._load_configurations()
        self._load_prompt_templates()

        # Ensure logging directories exist
        Path('data/audit_logs').mkdir(parents=True, exist_ok=True)
        Path('data/reports').mkdir(parents=True, exist_ok=True)

    def _load_configurations(self):
        """Load YAML configuration files, generate agent_profiles.yaml if needed"""
        # Ensure agent_profiles.yaml exists and is up-to-date
        self._ensure_agent_profiles()

        with open('config/agent_profiles.yaml', 'r', encoding='utf-8') as f:
            self.agent_profiles = yaml.safe_load(f)
        with open('config/thresholds.yaml', 'r', encoding='utf-8') as f:
            self.thresholds = yaml.safe_load(f)

    def _load_prompt_templates(self):
        """Load prompt templates from ContextGuardrail directory"""
        context_dir = Path('ContextGuardrail')

        with open(context_dir / 'base_instruction_prompt.txt', 'r', encoding='utf-8') as f:
            self.base_instruction = f.read()
        with open(context_dir / 'schema_violation_prompt.txt', 'r', encoding='utf-8') as f:
            self.schema_violation_template = f.read()
        with open(context_dir / 'hallucination_check_prompt.txt', 'r', encoding='utf-8') as f:
            self.hallucination_template = f.read()
        with open(context_dir / 'escalation_policy_prompt.txt', 'r', encoding='utf-8') as f:
            self.escalation_template = f.read()

    def _ensure_agent_profiles(self):
        """Ensure agent_profiles.yaml exists and is up-to-date with SP_Parser_Agent outputs"""
        profiles_path = Path('config/agent_profiles.yaml')
        schema_path = Path('test_data/schema_template.json')
        rules_path = Path('test_data/data_rules.json')

        # Check if we need to generate/regenerate profiles
        should_generate = (
            not profiles_path.exists() or
            (schema_path.exists() and rules_path.exists() and
             profiles_path.stat().st_mtime < max(schema_path.stat().st_mtime, rules_path.stat().st_mtime))
        )

        if should_generate and schema_path.exists() and rules_path.exists():
            print("🔄 Generating agent profiles from SP_Parser_Agent outputs...")
            self._generate_agent_profiles()
        elif not profiles_path.exists():
            print("⚠️  No agent_profiles.yaml found and no SP_Parser_Agent outputs available.")
            print("   Creating minimal default profiles...")
            self._create_default_profiles()

    def _generate_agent_profiles(self):
        """Generate agent profiles from SP_Parser_Agent outputs"""
        try:
            # Load SP_Parser_Agent outputs
            with open('test_data/schema_template.json', 'r') as f:
                schema_template = json.load(f)
            with open('test_data/data_rules.json', 'r') as f:
                data_rules = json.load(f)

            # Extract column information
            columns = self._extract_column_info(schema_template)

            # Generate profiles
            file_validation_profile = self._generate_file_validation_profile(columns, data_rules)
            data_validation_profile = self._generate_data_validation_profile(columns, data_rules)

            # Combine profiles
            agent_profiles = {}
            agent_profiles.update(file_validation_profile)
            agent_profiles.update(data_validation_profile)

            # Write to file
            Path('config').mkdir(exist_ok=True)
            with open('config/agent_profiles.yaml', 'w') as f:
                yaml.dump(agent_profiles, f, default_flow_style=False, indent=2, sort_keys=False)

            print(f"✅ Generated agent_profiles.yaml with {len(columns)} columns")

        except Exception as e:
            print(f"❌ Error generating agent profiles: {e}")
            self._create_default_profiles()

    def _extract_column_info(self, schema_template: List[Dict]) -> Dict[str, str]:
        """Extract column names and infer data types from schema template"""
        columns = {}

        for rule in schema_template:
            if rule.get('columns'):
                for column in rule['columns']:
                    # Infer data type based on column name patterns
                    if column in ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']:
                        columns[column] = 'number'
                    else:
                        columns[column] = 'string'

        return columns

    def _extract_constraints(self, data_rules: List[Dict], column: str) -> List[Dict[str, Any]]:
        """Extract constraints for a specific column from data rules"""
        constraints = []

        for rule in data_rules:
            if column in rule.get('columns', []):
                rule_type = rule.get('type')
                rule_text = rule.get('rule', '')

                if rule_type == 'null_check':
                    constraints.append({'not_null': True})
                elif rule_type == 'duplicate_check':
                    constraints.append({'unique': True})
                elif rule_type == 'format_check':
                    if 'alphanumeric' in rule_text.lower():
                        constraints.append({'pattern': '^[a-zA-Z0-9]+$'})
                    elif 'not start with' in rule_text.lower() and 'TOTAL' in rule_text:
                        constraints.append({'not_starts_with': 'TOTAL'})

        return constraints

    def audit_agent_output(self, agent_name: str, agent_phase: str, agent_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main audit method - AI-powered with minimal hardcoding.

        Uses AI to analyze agent output against configurations and determine findings.
        """
        try:
            # Get agent configuration
            agent_config = self.agent_profiles.get(agent_name, {})

            # Create structured audit prompt using templates
            schema_check_prompt = self.schema_violation_template.format(
                agent_name=agent_name,
                expected_schema=json.dumps(agent_config.get('expected_output_structure', {}), indent=2),
                agent_output=json.dumps(agent_output, indent=2)
            )

            hallucination_check_prompt = self.hallucination_template.format(
                agent_name=agent_name,
                agent_output=json.dumps(agent_output, indent=2),
                agent_capabilities=json.dumps(agent_config.get('capabilities', []), indent=2)
            )

            escalation_check_prompt = self.escalation_template.format(
                agent_name=agent_name,
                agent_phase=agent_phase,
                findings="[To be determined after analysis]",
                escalation_thresholds=json.dumps(self.thresholds.get('escalation_thresholds', {}), indent=2)
            )

            # Combine all structured prompts
            audit_prompt = f"""
            You are auditing the output of {agent_name} in {agent_phase}.

            AGENT CONFIGURATION:
            {json.dumps(agent_config, indent=2)}

            THRESHOLDS:
            {json.dumps(self.thresholds, indent=2)}

            AGENT OUTPUT TO AUDIT:
            {json.dumps(agent_output, indent=2)}

            Perform these structured checks:

            1. SCHEMA COMPLIANCE CHECK:
            {schema_check_prompt}

            2. HALLUCINATION DETECTION:
            {hallucination_check_prompt}

            3. ESCALATION ASSESSMENT:
            {escalation_check_prompt}

            Provide your analysis in this exact JSON format:
            {{
                "findings": ["finding 1", "finding 2", ...],
                "status": "PASSED" | "WARNING" | "CRITICAL",
                "escalation": "RECOMMENDED" | "NOT_NEEDED"
            }}

            Be concise and specific in findings.
            """

            # Get AI analysis
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.base_instruction},
                    {"role": "user", "content": audit_prompt}
                ],
                temperature=0.1
            )

            # Parse AI response
            ai_result = json.loads(response.choices[0].message.content)

            # Create final report
            report = {
                "agent": agent_name,
                "phase": agent_phase,
                "status": ai_result.get("status", "WARNING"),
                "findings": ai_result.get("findings", []),
                "escalation": ai_result.get("escalation", "NOT_NEEDED")
            }

            # Log the audit
            self._log_audit(report)

            return report

        except Exception as e:
            return {
                "agent": agent_name,
                "phase": agent_phase,
                "status": "CRITICAL",
                "findings": [f"Audit error: {str(e)}"],
                "escalation": "RECOMMENDED"
            }

    def _log_audit(self, report: Dict[str, Any]):
        """Simple logging for audit results"""
        timestamp = datetime.now(timezone.utc).isoformat()

        # JSON log for structured data
        json_log = {
            "timestamp": timestamp,
            "type": "audit_complete",
            "agent": report["agent"],
            "phase": report["phase"],
            "status": report["status"],
            "findings_count": len(report["findings"]),
            "escalation": report["escalation"]
        }

        # Human readable log
        status_marker = "[OK]" if report["status"] == "PASSED" else "[ESCALATE]" if report["escalation"] == "RECOMMENDED" else "[DONE]"
        human_log = f"{timestamp} {status_marker} Audit complete - {report['agent']} {report['status']} ({len(report['findings'])} findings)"

        # Write logs
        with open('data/audit_logs/audit_logs.jsonl', 'a', encoding='utf-8') as f:
            f.write(json.dumps(json_log, ensure_ascii=False) + '\n')

        with open('data/audit_logs/audit_human_readable.log', 'a', encoding='utf-8') as f:
            f.write(human_log + '\n')

        # Write full report
        report_filename = f"data/reports/audit_report_{report['agent']}_{timestamp.replace(':', '-')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

    def _generate_file_validation_profile(self, columns: Dict[str, str], data_rules: List[Dict]) -> Dict[str, Any]:
        """Generate FileValidationAgent profile"""
        properties = {}

        for column, data_type in columns.items():
            properties[column] = {'type': data_type}

            # Add constraints
            constraints = self._extract_constraints(data_rules, column)
            if constraints:
                properties[column]['constraints'] = constraints

        return {
            'FileValidationAgent': {
                'max_processing_time_ms': 5000,
                'min_coverage': 0.98,
                'capabilities': [
                    'File structure validation',
                    'Column presence verification',
                    'Data type validation',
                    'Basic format checking'
                ],
                'limitations': [
                    'Cannot validate business logic',
                    'Cannot check cross-record relationships',
                    'Cannot validate data quality beyond format'
                ],
                'expected_output_structure': {
                    'metadata': {
                        'required': True,
                        'type': 'object',
                        'fields': {
                            'agent_type': {'type': 'string', 'required': True, 'expected_value': 'FileValidationAgent'},
                            'agent_version': {'type': 'string', 'required': True},
                            'processing_time_ms': {'type': 'integer', 'required': True, 'min_value': 0},
                            'task_id': {'type': 'string', 'required': True},
                            'timestamp': {'type': 'string', 'required': True, 'format': 'ISO8601'}
                        }
                    },
                    'validation_coverage': {'type': 'number', 'required': True, 'min_value': 0.0, 'max_value': 1.0},
                    'valid_rows': {'type': 'integer', 'required': True, 'min_value': 0},
                    'invalid_rows': {'type': 'integer', 'required': True, 'min_value': 0},
                    'column_validation': {'type': 'object', 'required': True, 'properties': properties}
                }
            }
        }

    def _generate_data_validation_profile(self, columns: Dict[str, str], data_rules: List[Dict]) -> Dict[str, Any]:
        """Generate DataValidationAgent profile"""
        properties = {}
        required_fields = []

        for column, data_type in columns.items():
            properties[column] = {'type': data_type}

            # Add constraints
            constraints = self._extract_constraints(data_rules, column)
            if constraints:
                properties[column]['constraints'] = constraints

            required_fields.append(column)

        return {
            'DataValidationAgent': {
                'max_processing_time_ms': 10000,
                'max_override_rate': 0.05,
                'capabilities': [
                    'Business rule validation',
                    'Cross-record validation',
                    'Data quality assessment',
                    'Override management'
                ],
                'limitations': [
                    'Cannot modify source data',
                    'Cannot validate external references',
                    'Cannot perform complex calculations'
                ],
                'expected_output_structure': {
                    'metadata': {
                        'required': True,
                        'type': 'object',
                        'fields': {
                            'agent_type': {'type': 'string', 'required': True, 'expected_value': 'DataValidationAgent'},
                            'agent_version': {'type': 'string', 'required': True},
                            'processing_time_ms': {'type': 'integer', 'required': True, 'min_value': 0},
                            'task_id': {'type': 'string', 'required': True},
                            'timestamp': {'type': 'string', 'required': True, 'format': 'ISO8601'}
                        }
                    },
                    'validation_results': {
                        'type': 'array',
                        'required': True,
                        'items': {'type': 'object', 'required': required_fields, 'properties': properties}
                    },
                    'override_rate': {'type': 'number', 'required': True, 'min_value': 0.0, 'max_value': 1.0},
                    'override_justifications': {'type': 'array', 'required': True, 'items': {'type': 'string'}}
                }
            }
        }

    def _create_default_profiles(self):
        """Create minimal default profiles when SP_Parser_Agent outputs are not available"""
        default_profiles = {
            'FileValidationAgent': {
                'max_processing_time_ms': 5000,
                'min_coverage': 0.98,
                'capabilities': ['File structure validation', 'Column presence verification'],
                'limitations': ['Cannot validate business logic'],
                'expected_output_structure': {
                    'metadata': {'required': True, 'type': 'object'},
                    'validation_coverage': {'type': 'number', 'required': True},
                    'valid_rows': {'type': 'integer', 'required': True},
                    'invalid_rows': {'type': 'integer', 'required': True}
                }
            },
            'DataValidationAgent': {
                'max_processing_time_ms': 10000,
                'max_override_rate': 0.05,
                'capabilities': ['Business rule validation', 'Data quality assessment'],
                'limitations': ['Cannot modify source data'],
                'expected_output_structure': {
                    'metadata': {'required': True, 'type': 'object'},
                    'validation_results': {'type': 'array', 'required': True},
                    'override_rate': {'type': 'number', 'required': True}
                }
            }
        }

        Path('config').mkdir(exist_ok=True)
        with open('config/agent_profiles.yaml', 'w') as f:
            yaml.dump(default_profiles, f, default_flow_style=False, indent=2, sort_keys=False)

        print("✅ Created default agent_profiles.yaml")



