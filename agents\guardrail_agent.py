import openai
import yaml
import json
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
from typing import Dict, Any, Optional, List
from pathlib import Path
from pydantic import ValidationError

# Import Pydantic models for structured validation
from .models import (
    AuditReport, AuditLogEntry, AuditStatus, EscalationStatus,
    FileValidationOutput, DataValidationOutput, AIAnalysisResult,
    GuardrailConfig, AgentProfile, ThresholdConfig
)

# Load environment variables from .env file
load_dotenv()

class GuardrailAgent:
    """
    AI-powered reasoning-based audit agent for validating outputs from autonomous validation agents.

    This agent is purely AI-driven with minimal hardcoding. It uses structured prompts and
    configuration-driven logic to audit agent outputs without redoing their work.
    """

    def __init__(self, api_key: Optional[str] = None):
        # Use provided API key or load from environment
        if api_key is None:
            api_key = os.getenv('OPENAI_API_KEY')

        if not api_key:
            raise ValueError("OpenAI API key must be provided either as parameter or OPENAI_API_KEY environment variable")

        self.client = openai.OpenAI(api_key=api_key)

        # Get model from environment or use default
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
        self.temperature = float(os.getenv('AI_TEMPERATURE', '0.1'))
        self.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        self.timeout_seconds = int(os.getenv('AUDIT_TIMEOUT_SECONDS', '30'))

        # Load configurations and prompts
        self._load_configurations()
        self._load_prompt_templates()

        # Ensure logging directories exist
        self.audit_logs_dir = Path('data/audit_logs')
        self.reports_dir = Path('data/reports')
        self.audit_logs_dir.mkdir(parents=True, exist_ok=True)
        self.reports_dir.mkdir(parents=True, exist_ok=True)

        # Initialize log file paths
        self.json_log_file = self.audit_logs_dir / 'audit_logs.jsonl'
        self.human_log_file = self.audit_logs_dir / 'audit_human_readable.log'

    def _load_configurations(self):
        """Load YAML configuration files, generate agent_profiles.yaml if needed"""
        # Ensure agent_profiles.yaml exists and is up-to-date
        self._ensure_agent_profiles()

        with open('config/agent_profiles.yaml', 'r', encoding='utf-8') as f:
            self.agent_profiles = yaml.safe_load(f)
        with open('config/thresholds.yaml', 'r', encoding='utf-8') as f:
            self.thresholds = yaml.safe_load(f)

    def _load_prompt_templates(self):
        """Load prompt templates from ContextGuardrail directory"""
        context_dir = Path('ContextGuardrail')

        with open(context_dir / 'base_instruction_prompt.txt', 'r', encoding='utf-8') as f:
            self.base_instruction = f.read()
        with open(context_dir / 'schema_violation_prompt.txt', 'r', encoding='utf-8') as f:
            self.schema_violation_template = f.read()
        with open(context_dir / 'hallucination_check_prompt.txt', 'r', encoding='utf-8') as f:
            self.hallucination_template = f.read()
        with open(context_dir / 'escalation_policy_prompt.txt', 'r', encoding='utf-8') as f:
            self.escalation_template = f.read()

    def _ensure_agent_profiles(self):
        """Ensure agent_profiles.yaml exists and is up-to-date with SP_Parser_Agent outputs"""
        profiles_path = Path('config/agent_profiles.yaml')
        schema_path = Path('test_data/schema_template.json')
        rules_path = Path('test_data/data_rules.json')

        # Check if we need to generate/regenerate profiles
        should_generate = (
            not profiles_path.exists() or
            (schema_path.exists() and rules_path.exists() and
             profiles_path.stat().st_mtime < max(schema_path.stat().st_mtime, rules_path.stat().st_mtime))
        )

        if should_generate and schema_path.exists() and rules_path.exists():
            print("🔄 Generating agent profiles from SP_Parser_Agent outputs...")
            self._generate_agent_profiles()
        elif not profiles_path.exists():
            print("⚠️  No agent_profiles.yaml found and no SP_Parser_Agent outputs available.")
            print("   Creating minimal default profiles...")
            self._create_default_profiles()

    def _generate_agent_profiles(self):
        """Generate agent profiles from SP_Parser_Agent outputs"""
        try:
            # Load SP_Parser_Agent outputs
            with open('test_data/schema_template.json', 'r') as f:
                schema_template = json.load(f)
            with open('test_data/data_rules.json', 'r') as f:
                data_rules = json.load(f)

            # Extract column information
            columns = self._extract_column_info(schema_template)

            # Generate profiles
            file_validation_profile = self._generate_file_validation_profile(columns, data_rules)
            data_validation_profile = self._generate_data_validation_profile(columns, data_rules)

            # Combine profiles
            agent_profiles = {}
            agent_profiles.update(file_validation_profile)
            agent_profiles.update(data_validation_profile)

            # Write to file
            Path('config').mkdir(exist_ok=True)
            with open('config/agent_profiles.yaml', 'w') as f:
                yaml.dump(agent_profiles, f, default_flow_style=False, indent=2, sort_keys=False)

            print(f"✅ Generated agent_profiles.yaml with {len(columns)} columns")

        except Exception as e:
            print(f"❌ Error generating agent profiles: {e}")
            self._create_default_profiles()

    def _extract_column_info(self, schema_template: List[Dict]) -> Dict[str, str]:
        """Extract column names and infer data types from schema template"""
        columns = {}

        for rule in schema_template:
            if rule.get('columns'):
                for column in rule['columns']:
                    # Infer data type based on column name patterns
                    if column in ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']:
                        columns[column] = 'number'
                    else:
                        columns[column] = 'string'

        return columns

    def _extract_constraints(self, data_rules: List[Dict], column: str) -> List[Dict[str, Any]]:
        """Extract constraints for a specific column from data rules"""
        constraints = []

        for rule in data_rules:
            if column in rule.get('columns', []):
                rule_type = rule.get('type')
                rule_text = rule.get('rule', '')

                if rule_type == 'null_check':
                    constraints.append({'not_null': True})
                elif rule_type == 'duplicate_check':
                    constraints.append({'unique': True})
                elif rule_type == 'format_check':
                    if 'alphanumeric' in rule_text.lower():
                        constraints.append({'pattern': '^[a-zA-Z0-9]+$'})
                    elif 'not start with' in rule_text.lower() and 'TOTAL' in rule_text:
                        constraints.append({'not_starts_with': 'TOTAL'})

        return constraints

    def audit_agent_output(self, agent_name: str, agent_phase: str, agent_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main audit method - AI-powered with minimal hardcoding.

        Uses AI to analyze agent output against configurations and determine findings.
        """
        start_time = datetime.now(timezone.utc)

        # Log audit start
        self._log_audit_start(agent_name, agent_phase)

        try:
            # Validate and parse agent output based on agent type
            parsed_output = self._parse_agent_output(agent_name, agent_output)

            # Get agent configuration
            agent_config = self.agent_profiles.get(agent_name, {})
            if not agent_config:
                return self._create_error_report(
                    agent_name, agent_phase,
                    f"No configuration found for agent: {agent_name}",
                    start_time
                )

            # Create structured audit prompt using templates
            schema_check_prompt = self.schema_violation_template.format(
                agent_name=agent_name,
                expected_schema=json.dumps(agent_config.get('expected_output_structure', {}), indent=2),
                agent_output=json.dumps(agent_output, indent=2)
            )

            hallucination_check_prompt = self.hallucination_template.format(
                agent_name=agent_name,
                agent_output=json.dumps(agent_output, indent=2),
                agent_capabilities=json.dumps(agent_config.get('capabilities', []), indent=2)
            )

            escalation_check_prompt = self.escalation_template.format(
                agent_name=agent_name,
                agent_phase=agent_phase,
                findings="[To be determined after analysis]",
                escalation_thresholds=json.dumps(self.thresholds.get('escalation_thresholds', {}), indent=2)
            )

            # Combine all structured prompts
            audit_prompt = f"""
            You are auditing the output of {agent_name} in {agent_phase}.

            AGENT CONFIGURATION:
            {json.dumps(agent_config, indent=2)}

            THRESHOLDS:
            {json.dumps(self.thresholds, indent=2)}

            AGENT OUTPUT TO AUDIT:
            {json.dumps(agent_output, indent=2)}

            Perform these structured checks:

            1. SCHEMA COMPLIANCE CHECK:
            {schema_check_prompt}

            2. HALLUCINATION DETECTION:
            {hallucination_check_prompt}

            3. ESCALATION ASSESSMENT:
            {escalation_check_prompt}

            Provide your analysis in this exact JSON format:
            {{
                "findings": ["finding 1", "finding 2", ...],
                "status": "PASSED" | "WARNING" | "CRITICAL",
                "escalation": "RECOMMENDED" | "NOT_NEEDED"
            }}

            Be concise and specific in findings.
            """

            # Get AI analysis with retry logic
            ai_result = self._get_ai_analysis(audit_prompt)

            # Calculate processing time
            processing_time_ms = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)

            # Create final report using Pydantic model
            report = AuditReport(
                agent=agent_name,
                phase=agent_phase,
                status=AuditStatus(ai_result.status),
                findings=ai_result.findings,
                escalation=EscalationStatus(ai_result.escalation),
                processing_time_ms=processing_time_ms
            )

            # Log the audit
            self._log_audit(report)

            return report.dict()

        except Exception as e:
            return self._create_error_report(agent_name, agent_phase, str(e), start_time)

    def _parse_agent_output(self, agent_name: str, agent_output: Dict[str, Any]) -> Dict[str, Any]:
        """Parse and validate agent output based on agent type"""
        try:
            if agent_name == "FileValidationAgent":
                # Try to parse as FileValidationOutput, but don't fail if it doesn't match exactly
                return agent_output
            elif agent_name == "DataValidationAgent":
                # Try to parse as DataValidationOutput, but don't fail if it doesn't match exactly
                return agent_output
            else:
                return agent_output
        except ValidationError as e:
            # Log validation error but continue with raw output
            self._log_warning(f"Agent output validation failed for {agent_name}: {e}")
            return agent_output

    def _get_ai_analysis(self, audit_prompt: str) -> AIAnalysisResult:
        """Get AI analysis with retry logic and proper error handling"""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": self.base_instruction},
                        {"role": "user", "content": audit_prompt}
                    ],
                    temperature=self.temperature,
                    timeout=self.timeout_seconds
                )

                # Parse AI response
                response_content = response.choices[0].message.content.strip()

                # Try to extract JSON from response
                ai_data = self._extract_json_from_response(response_content)

                # Validate and create AIAnalysisResult
                return AIAnalysisResult(
                    findings=ai_data.get("findings", []),
                    status=AuditStatus(ai_data.get("status", "WARNING")),
                    escalation=EscalationStatus(ai_data.get("escalation", "NOT_NEEDED"))
                )

            except Exception as e:
                last_error = e
                self._log_warning(f"AI analysis attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    continue

        # If all retries failed, return a default critical result
        return AIAnalysisResult(
            findings=[f"AI analysis failed after {self.max_retries} attempts: {last_error}"],
            status=AuditStatus.CRITICAL,
            escalation=EscalationStatus.RECOMMENDED
        )

    def _extract_json_from_response(self, response_content: str) -> Dict[str, Any]:
        """Extract JSON from AI response, handling various formats"""
        try:
            # Try direct JSON parsing first
            return json.loads(response_content)
        except json.JSONDecodeError:
            # Try to find JSON within the response
            import re
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass

            # If no valid JSON found, create a default response
            return {
                "findings": [f"Could not parse AI response: {response_content[:200]}..."],
                "status": "WARNING",
                "escalation": "NOT_NEEDED"
            }

    def _create_error_report(self, agent_name: str, agent_phase: str, error_message: str, start_time: datetime) -> Dict[str, Any]:
        """Create an error report when audit fails"""
        processing_time_ms = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)

        report = AuditReport(
            agent=agent_name,
            phase=agent_phase,
            status=AuditStatus.CRITICAL,
            findings=[f"Audit error: {error_message}"],
            escalation=EscalationStatus.RECOMMENDED,
            processing_time_ms=processing_time_ms
        )

        # Log the error report
        self._log_audit(report)
        return report.dict()

    def _log_audit_start(self, agent_name: str, agent_phase: str):
        """Log the start of an audit"""
        timestamp = datetime.now(timezone.utc).isoformat()
        human_log = f"{timestamp} [START] Audit starting - {agent_name} {agent_phase}"

        try:
            with open(self.human_log_file, 'a', encoding='utf-8') as f:
                f.write(human_log + '\n')
        except Exception as e:
            print(f"Warning: Could not write to human log file: {e}")

    def _log_warning(self, message: str):
        """Log a warning message"""
        timestamp = datetime.now(timezone.utc).isoformat()
        human_log = f"{timestamp} [WARNING] {message}"

        try:
            with open(self.human_log_file, 'a', encoding='utf-8') as f:
                f.write(human_log + '\n')
        except Exception as e:
            print(f"Warning: Could not write to human log file: {e}")

    def _log_audit(self, report: AuditReport):
        """Enhanced dual logging for audit results following user preferences"""
        timestamp = datetime.now(timezone.utc).isoformat()

        try:
            # Create structured JSON log entry
            json_log_entry = AuditLogEntry(
                timestamp=timestamp,
                type="audit_complete",
                agent=report.agent,
                phase=report.phase,
                status=report.status.value,
                findings_count=len(report.findings),
                escalation=report.escalation.value,
                processing_time_ms=report.processing_time_ms
            )

            # Create human-readable log with status markers (following user preferences)
            if report.status == AuditStatus.PASSED:
                status_marker = "[OK]"
            elif report.escalation == EscalationStatus.RECOMMENDED:
                status_marker = "[ESCALATE]"
            else:
                status_marker = "[DONE]"

            human_log = f"{timestamp} {status_marker} Audit complete - {report.agent} {report.status.value} ({len(report.findings)} findings)"

            # Write structured JSON log (for traceability)
            try:
                with open(self.json_log_file, 'a', encoding='utf-8') as f:
                    f.write(json_log_entry.json(ensure_ascii=False) + '\n')
            except Exception as e:
                print(f"Error writing JSON log: {e}")

            # Write human-readable log (manager/demo-friendly formatting)
            try:
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(human_log + '\n')
            except Exception as e:
                print(f"Error writing human log: {e}")

            # Write detailed audit report
            safe_timestamp = timestamp.replace(':', '-').replace('.', '-')
            report_filename = self.reports_dir / f"audit_report_{report.agent}_{safe_timestamp}.json"

            try:
                with open(report_filename, 'w', encoding='utf-8') as f:
                    # Convert to dict for JSON serialization
                    report_dict = report.dict()
                    # Ensure datetime is serialized properly
                    if 'audit_timestamp' in report_dict and report_dict['audit_timestamp']:
                        report_dict['audit_timestamp'] = report_dict['audit_timestamp'].isoformat()
                    json.dump(report_dict, f, indent=2, ensure_ascii=False)

                # Log successful report creation
                success_log = f"{timestamp} [STEP] Report saved - {report_filename.name}"
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(success_log + '\n')

            except Exception as e:
                error_log = f"{timestamp} [ERROR] Failed to save report: {e}"
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(error_log + '\n')
                print(f"Error writing audit report: {e}")

        except Exception as e:
            # Fallback logging if structured logging fails
            fallback_log = f"{timestamp} [ERROR] Logging failed for {report.agent}: {e}"
            try:
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(fallback_log + '\n')
            except:
                print(fallback_log)

    def _generate_file_validation_profile(self, columns: Dict[str, str], data_rules: List[Dict]) -> Dict[str, Any]:
        """Generate FileValidationAgent profile based on actual output format"""
        return {
            'FileValidationAgent': {
                'max_processing_time_ms': 5000,
                'min_coverage': 0.98,
                'capabilities': [
                    'File structure validation',
                    'Column presence verification',
                    'Data type validation',
                    'Basic format checking'
                ],
                'limitations': [
                    'Cannot validate business logic',
                    'Cannot check cross-record relationships',
                    'Cannot validate data quality beyond format'
                ],
                'expected_output_structure': {
                    'success': {'type': 'boolean', 'required': True},
                    'message': {'type': 'string', 'required': True},
                    'validation_details': {
                        'type': 'object',
                        'required': True,
                        'properties': {
                            'column_presence_and_order': {
                                'type': 'object',
                                'properties': {
                                    'status': {'type': 'string', 'required': True},
                                    'details': {'type': 'string', 'required': True}
                                }
                            },
                            'data_types': {
                                'type': 'object',
                                'properties': {
                                    'status': {'type': 'string', 'required': True},
                                    'details': {'type': 'array', 'required': True}
                                }
                            },
                            'required_constraints': {
                                'type': 'object',
                                'properties': {
                                    'status': {'type': 'string', 'required': True},
                                    'violations': {'type': 'array', 'required': False}
                                }
                            }
                        }
                    },
                    'verdict': {'type': 'string', 'required': True},
                    'logs': {'type': 'object', 'required': False}
                }
            }
        }

    def _generate_data_validation_profile(self, columns: Dict[str, str], data_rules: List[Dict]) -> Dict[str, Any]:
        """Generate DataValidationAgent profile based on actual output format"""
        # Build properties for each data column plus validation fields
        properties = {}

        for column, data_type in columns.items():
            properties[column] = {'type': data_type, 'required': False}  # Can be null in validation output

        # Add validation-specific fields
        properties['Is_correct'] = {'type': 'boolean', 'required': True}
        properties['Why'] = {'type': 'string', 'required': True}

        return {
            'DataValidationAgent': {
                'max_processing_time_ms': 10000,
                'max_override_rate': 0.05,
                'capabilities': [
                    'Business rule validation',
                    'Cross-record validation',
                    'Data quality assessment',
                    'Duplicate detection',
                    'Null value validation'
                ],
                'limitations': [
                    'Cannot modify source data',
                    'Cannot validate external references',
                    'Cannot perform complex calculations'
                ],
                'expected_output_structure': {
                    'type': 'array',
                    'required': True,
                    'description': 'Array of validated records with validation results',
                    'items': {
                        'type': 'object',
                        'properties': properties,
                        'required': ['Is_correct', 'Why']
                    }
                }
            }
        }

    def _create_default_profiles(self):
        """Create minimal default profiles when SP_Parser_Agent outputs are not available"""
        default_profiles = {
            'FileValidationAgent': {
                'max_processing_time_ms': 5000,
                'min_coverage': 0.98,
                'capabilities': ['File structure validation', 'Column presence verification', 'Data type validation'],
                'limitations': ['Cannot validate business logic'],
                'expected_output_structure': {
                    'success': {'type': 'boolean', 'required': True},
                    'message': {'type': 'string', 'required': True},
                    'validation_details': {'type': 'object', 'required': True},
                    'verdict': {'type': 'string', 'required': True}
                }
            },
            'DataValidationAgent': {
                'max_processing_time_ms': 10000,
                'max_override_rate': 0.05,
                'capabilities': ['Business rule validation', 'Data quality assessment', 'Duplicate detection'],
                'limitations': ['Cannot modify source data'],
                'expected_output_structure': {
                    'type': 'array',
                    'required': True,
                    'items': {
                        'type': 'object',
                        'properties': {
                            'Is_correct': {'type': 'boolean', 'required': True},
                            'Why': {'type': 'string', 'required': True}
                        }
                    }
                }
            }
        }

        Path('config').mkdir(exist_ok=True)
        with open('config/agent_profiles.yaml', 'w') as f:
            yaml.dump(default_profiles, f, default_flow_style=False, indent=2, sort_keys=False)

        print("✅ Created default agent_profiles.yaml")



