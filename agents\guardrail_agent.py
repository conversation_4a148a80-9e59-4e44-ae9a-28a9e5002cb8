"""
AI-Powered GuardrailAgent - Pure AI Reasoning with No Hardcoding

This module contains the GuardrailAgent class that performs intelligent auditing
of validation agent outputs using pure AI reasoning without hardcoded rules.
"""

import json
import os
import re
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Optional

import openai
import yaml
from dotenv import load_dotenv

from .models import (
    AIAnalysisResult,
    AuditLogEntry,
    AuditReport,
    AuditStatus,
    EscalationStatus,
)

# Load environment variables
load_dotenv()


class GuardrailAgent:
    """
    AI-powered audit agent that uses pure reasoning to validate agent outputs.

    Key Features:
    - Zero hardcoded validation rules
    - AI-driven schema analysis
    - Dynamic threshold assessment
    - Configuration-guided reasoning
    - Dual logging (JSON + human-readable)
    """

    def __init__(self, api_key: Optional[str] = None):
        """Initialize the AI-powered GuardrailAgent."""
        # Setup OpenAI client
        api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError(
                "OpenAI API key required. Set OPENAI_API_KEY environment variable "
                "or pass api_key parameter."
            )

        self.client = openai.OpenAI(api_key=api_key)

        # Load AI configuration from environment
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
        self.temperature = float(os.getenv('AI_TEMPERATURE', '0.1'))
        self.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        self.timeout_seconds = int(os.getenv('AUDIT_TIMEOUT_SECONDS', '30'))

        # Setup data directories
        self._setup_directories()

        # Load configurations and prompts
        self._load_configurations()
        self._load_prompt_templates()

    def _setup_directories(self):
        """Create necessary directories and initialize file paths."""
        self.audit_logs_dir = Path('data/audit_logs')
        self.reports_dir = Path('data/reports')

        # Create directories if they don't exist
        self.audit_logs_dir.mkdir(parents=True, exist_ok=True)
        self.reports_dir.mkdir(parents=True, exist_ok=True)

        # Initialize log file paths
        self.json_log_file = self.audit_logs_dir / 'audit_logs.jsonl'
        self.human_log_file = self.audit_logs_dir / 'audit_human_readable.log'

    def _load_configurations(self):
        """Load YAML configuration files with robust error handling."""
        try:
            self._ensure_agent_profiles()

            # Load agent profiles
            profiles_path = Path('config/agent_profiles.yaml')
            self.agent_profiles = self._load_yaml_file(profiles_path, "agent profiles")

            # Load thresholds
            thresholds_path = Path('config/thresholds.yaml')
            self.thresholds = self._load_yaml_file(thresholds_path, "thresholds")

            print(f"✅ Loaded configurations: {len(self.agent_profiles)} agent profiles, "
                  f"{len(self.thresholds)} threshold categories")

        except Exception as e:
            print(f"❌ Configuration loading failed: {e}")
            self._use_fallback_configurations()

    def _load_yaml_file(self, file_path: Path, description: str) -> Dict[str, Any]:
        """Load and validate a YAML file."""
        if not file_path.exists():
            raise FileNotFoundError(f"{description.title()} file not found: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)

        if not data:
            raise ValueError(f"{description.title()} file is empty or invalid")

        return data

    def _use_fallback_configurations(self):
        """Set minimal fallback configurations when loading fails."""
        self.agent_profiles = {}
        self.thresholds = {
            'escalation_thresholds': {'critical_issues': 1, 'warning_issues': 3},
            'performance_thresholds': {},
            'schema_thresholds': {'missing_required_fields': 0}
        }
        print("⚠️  Using minimal fallback configurations")

    def _load_prompt_templates(self):
        """Load structured prompt templates from ContextGuardrail directory."""
        context_dir = Path('ContextGuardrail')

        template_files = {
            'base_instruction': 'base_instruction_prompt.txt',
            'schema_violation_template': 'schema_violation_prompt.txt',
            'hallucination_template': 'hallucination_check_prompt.txt',
            'escalation_template': 'escalation_policy_prompt.txt'
        }

        for attr_name, filename in template_files.items():
            file_path = context_dir / filename
            with open(file_path, 'r', encoding='utf-8') as f:
                setattr(self, attr_name, f.read())

    def _ensure_agent_profiles(self):
        """Ensure agent profiles exist, generating them dynamically if needed."""
        profiles_path = Path('config/agent_profiles.yaml')
        schema_path = Path('test_data/schema_template.json')
        rules_path = Path('test_data/data_rules.json')

        # Check if profiles need generation/regeneration
        should_generate = self._should_regenerate_profiles(profiles_path, schema_path, rules_path)

        if should_generate and schema_path.exists() and rules_path.exists():
            print("🔄 Generating AI-driven agent profiles from SP_Parser_Agent outputs...")
            self._generate_agent_profiles()
        elif not profiles_path.exists():
            print("⚠️  Creating minimal AI-driven profiles...")
            self._create_minimal_profiles()

    def _should_regenerate_profiles(self, profiles_path: Path, schema_path: Path, rules_path: Path) -> bool:
        """Determine if agent profiles need to be regenerated."""
        if not profiles_path.exists():
            return True

        if not (schema_path.exists() and rules_path.exists()):
            return False

        # Regenerate if source files are newer than profiles
        profiles_mtime = profiles_path.stat().st_mtime
        source_mtime = max(schema_path.stat().st_mtime, rules_path.stat().st_mtime)
        return profiles_mtime < source_mtime

    def _generate_agent_profiles(self):
        """Generate agent profiles using AI analysis of SP_Parser_Agent outputs."""
        try:
            # Load source data
            schema_template = self._load_json_file('test_data/schema_template.json')
            data_rules = self._load_json_file('test_data/data_rules.json')

            # Create AI prompt for profile generation
            ai_prompt = self._create_profile_generation_prompt(schema_template, data_rules)

            # Get AI-generated profiles
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at analyzing data schemas and generating "
                                 "agent configurations. Generate practical, realistic agent profiles."
                    },
                    {"role": "user", "content": ai_prompt}
                ],
                temperature=0.1
            )

            # Parse and save AI-generated profiles
            agent_profiles = self._parse_ai_yaml_response(response.choices[0].message.content)
            self._save_agent_profiles(agent_profiles)

            print(f"✅ AI-generated agent_profiles.yaml with {len(agent_profiles)} agent types")

        except Exception as e:
            print(f"❌ Error generating AI-based agent profiles: {e}")
            self._create_minimal_profiles()

    def _load_json_file(self, file_path: str) -> Dict[str, Any]:
        """Load and parse a JSON file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _create_profile_generation_prompt(self, schema_template: Dict, data_rules: Dict) -> str:
        """Create the AI prompt for generating agent profiles."""
        return f"""
        Based on the following SP_Parser_Agent outputs, generate agent profiles for
        FileValidationAgent and DataValidationAgent.

        Schema Template:
        {json.dumps(schema_template, indent=2)}

        Data Rules:
        {json.dumps(data_rules, indent=2)}

        Generate a YAML configuration that defines:
        1. Capabilities and limitations for each agent
        2. Expected output structures based on what these agents would logically produce
        3. Performance thresholds

        Return as valid YAML format for agent_profiles.yaml
        """

    def _parse_ai_yaml_response(self, response_content: str) -> Dict[str, Any]:
        """Parse YAML from AI response, handling markdown wrapping."""
        yaml_content = response_content.strip()

        # Extract YAML from markdown code blocks if present
        if '```yaml' in yaml_content:
            yaml_content = yaml_content.split('```yaml')[1].split('```')[0].strip()
        elif '```' in yaml_content:
            yaml_content = yaml_content.split('```')[1].split('```')[0].strip()

        return yaml.safe_load(yaml_content)

    def _save_agent_profiles(self, agent_profiles: Dict[str, Any]):
        """Save agent profiles to YAML file."""
        Path('config').mkdir(exist_ok=True)
        with open('config/agent_profiles.yaml', 'w', encoding='utf-8') as f:
            yaml.dump(agent_profiles, f, default_flow_style=False, indent=2, sort_keys=False)

    def _create_minimal_profiles(self):
        """Create minimal profiles when AI generation fails."""
        minimal_profiles = {
            'FileValidationAgent': {
                'capabilities': ['File validation', 'Schema checking'],
                'limitations': ['Cannot validate business logic'],
                'expected_output_structure': 'AI will analyze actual output format'
            },
            'DataValidationAgent': {
                'capabilities': ['Data validation', 'Business rule checking'],
                'limitations': ['Cannot modify source data'],
                'expected_output_structure': 'AI will analyze actual output format'
            }
        }

        self._save_agent_profiles(minimal_profiles)
        print("✅ Created minimal AI-driven agent_profiles.yaml")

    def audit_agent_output(self, agent_name: str, agent_phase: str, agent_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main audit method using pure AI reasoning.

        Args:
            agent_name: Name of the agent being audited
            agent_phase: Phase identifier for the audit
            agent_output: Raw output from the agent to audit

        Returns:
            Audit report dictionary with findings, status, and escalation recommendation
        """
        start_time = datetime.now(timezone.utc)
        self._log_audit_start(agent_name, agent_phase)

        try:
            # Get agent configuration
            agent_config = self.agent_profiles.get(agent_name, {})
            if not agent_config:
                return self._create_error_report(
                    agent_name, agent_phase,
                    f"No configuration found for agent: {agent_name}",
                    start_time
                )

            # Create AI audit prompt
            audit_prompt = self._create_audit_prompt(
                agent_name, agent_phase, agent_config, agent_output
            )

            # Get AI analysis
            ai_result = self._get_ai_analysis(audit_prompt)

            # Create and log final report
            report = self._create_audit_report(
                agent_name, agent_phase, ai_result, start_time
            )

            self._log_audit(report)
            return report.model_dump()

        except Exception as e:
            return self._create_error_report(agent_name, agent_phase, str(e), start_time)

    def _create_audit_prompt(self, agent_name: str, agent_phase: str,
                           agent_config: Dict[str, Any], agent_output: Dict[str, Any]) -> str:
        """Create the AI audit prompt with structured guidance."""
        # Generate structured prompts using templates
        schema_check_prompt = self.schema_violation_template.format(
            agent_name=agent_name,
            expected_schema=json.dumps(agent_config.get('expected_output_structure', {}), indent=2),
            agent_output=json.dumps(agent_output, indent=2)
        )

        hallucination_check_prompt = self.hallucination_template.format(
            agent_name=agent_name,
            agent_output=json.dumps(agent_output, indent=2),
            agent_capabilities=json.dumps(agent_config.get('capabilities', []), indent=2)
        )

        escalation_check_prompt = self.escalation_template.format(
            agent_name=agent_name,
            agent_phase=agent_phase,
            findings="[To be determined after analysis]",
            escalation_thresholds=json.dumps(self.thresholds.get('escalation_thresholds', {}), indent=2)
        )

        return f"""
        You are auditing the output of {agent_name} in {agent_phase}.

        AGENT CONFIGURATION (use this to understand capabilities and limitations):
        {json.dumps(agent_config, indent=2)}

        THRESHOLDS (use these for determining severity):
        {json.dumps(self.thresholds, indent=2)}

        AGENT OUTPUT TO AUDIT:
        {json.dumps(agent_output, indent=2)}

        STRUCTURED PROMPTS FOR GUIDANCE:

        Schema Check: {schema_check_prompt}

        Hallucination Check: {hallucination_check_prompt}

        Escalation Assessment: {escalation_check_prompt}

        Using your AI reasoning, analyze this agent output completely. Consider:
        - Does the output structure make sense for this agent type?
        - Are there any logical inconsistencies or impossible values?
        - Does the output align with the agent's stated capabilities?
        - Are there any patterns that suggest errors or hallucinations?
        - Based on the thresholds, what severity level is appropriate?
        - Should this be escalated to humans?

        Provide your analysis in this exact JSON format:
        {{
            "findings": ["finding 1", "finding 2", ...],
            "status": "PASSED" | "WARNING" | "CRITICAL",
            "escalation": "RECOMMENDED" | "NOT_NEEDED"
        }}

        Use your reasoning to determine everything - no hardcoded rules.
        """

    def _create_audit_report(self, agent_name: str, agent_phase: str,
                           ai_result: AIAnalysisResult, start_time: datetime) -> AuditReport:
        """Create the final audit report."""
        processing_time_ms = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)

        return AuditReport(
            agent=agent_name,
            phase=agent_phase,
            status=AuditStatus(ai_result.status),
            findings=ai_result.findings,
            escalation=EscalationStatus(ai_result.escalation),
            processing_time_ms=processing_time_ms
        )

    def _parse_agent_output(self, agent_output: Dict[str, Any]) -> Dict[str, Any]:
        """Parse agent output - purely pass-through, let AI handle all validation."""
        # No hardcoded parsing - AI will analyze the raw output against configurations
        return agent_output

    def _get_ai_analysis(self, audit_prompt: str) -> AIAnalysisResult:
        """Get AI analysis with retry logic."""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": self.base_instruction},
                        {"role": "user", "content": audit_prompt}
                    ],
                    temperature=self.temperature,
                    timeout=self.timeout_seconds
                )

                # Parse and validate AI response
                response_content = response.choices[0].message.content.strip()
                ai_data = self._extract_json_from_response(response_content)

                return self._create_ai_analysis_result(ai_data)

            except Exception as e:
                last_error = e
                self._log_warning(f"AI analysis attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    continue

        # Return fallback result if all attempts failed
        return AIAnalysisResult(
            findings=[f"Technical issue prevented AI analysis: {last_error}"],
            status=AuditStatus.CRITICAL,
            escalation=EscalationStatus.RECOMMENDED
        )

    def _create_ai_analysis_result(self, ai_data: Dict[str, Any]) -> AIAnalysisResult:
        """Create AIAnalysisResult from parsed AI response data."""
        # Process findings - ensure they are strings
        findings = ai_data.get("findings", ["AI provided no specific findings"])
        if isinstance(findings, list):
            findings = [str(f) if not isinstance(f, str) else f for f in findings]
        else:
            findings = [str(findings)]

        return AIAnalysisResult(
            findings=findings,
            status=AuditStatus(ai_data.get("status", "WARNING")),
            escalation=EscalationStatus(ai_data.get("escalation", "NOT_NEEDED"))
        )

    def _extract_json_from_response(self, response_content: str) -> Dict[str, Any]:
        """Extract JSON from AI response with flexible parsing."""
        try:
            return json.loads(response_content)
        except json.JSONDecodeError:
            # Try to find JSON within the response using regex
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass

            # Fallback for unparseable responses
            return {
                "findings": [f"AI response format issue - Raw response: {response_content[:300]}"],
                "status": "WARNING",
                "escalation": "NOT_NEEDED"
            }

    def _create_error_report(self, agent_name: str, agent_phase: str,
                           error_message: str, start_time: datetime) -> Dict[str, Any]:
        """Create an error report when audit fails."""
        processing_time_ms = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)

        report = AuditReport(
            agent=agent_name,
            phase=agent_phase,
            status=AuditStatus.CRITICAL,
            findings=[f"Audit error: {error_message}"],
            escalation=EscalationStatus.RECOMMENDED,
            processing_time_ms=processing_time_ms
        )

        self._log_audit(report)
        return report.model_dump()

    def _log_audit_start(self, agent_name: str, agent_phase: str):
        """Log the start of an audit."""
        timestamp = datetime.now(timezone.utc).isoformat()
        log_message = f"{timestamp} [START] Audit starting - {agent_name} {agent_phase}"
        self._write_to_human_log(log_message)

    def _log_warning(self, message: str):
        """Log a warning message."""
        timestamp = datetime.now(timezone.utc).isoformat()
        log_message = f"{timestamp} [WARNING] {message}"
        self._write_to_human_log(log_message)

    def _write_to_human_log(self, message: str):
        """Write a message to the human-readable log file."""
        try:
            with open(self.human_log_file, 'a', encoding='utf-8') as f:
                f.write(message + '\n')
        except Exception as e:
            print(f"Warning: Could not write to human log file: {e}")

    def _log_audit(self, report: AuditReport):
        """Enhanced dual logging for audit results following user preferences"""
        timestamp = datetime.now(timezone.utc).isoformat()

        try:
            # Create structured JSON log entry
            status_str = report.status.value if hasattr(report.status, 'value') else str(report.status)
            escalation_str = report.escalation.value if hasattr(report.escalation, 'value') else str(report.escalation)

            json_log_entry = AuditLogEntry(
                timestamp=timestamp,
                type="audit_complete",
                agent=report.agent,
                phase=report.phase,
                status=status_str,
                findings_count=len(report.findings),
                escalation=escalation_str,
                processing_time_ms=report.processing_time_ms
            )

            # Create human-readable log with status markers (following user preferences)
            if report.status == AuditStatus.PASSED:
                status_marker = "[OK]"
            elif report.escalation == EscalationStatus.RECOMMENDED:
                status_marker = "[ESCALATE]"
            else:
                status_marker = "[DONE]"

            # Handle enum values properly
            status_str = report.status.value if hasattr(report.status, 'value') else str(report.status)
            human_log = f"{timestamp} {status_marker} Audit complete - {report.agent} {status_str} ({len(report.findings)} findings)"

            # Write structured JSON log (for traceability)
            try:
                with open(self.json_log_file, 'a', encoding='utf-8') as f:
                    f.write(json_log_entry.model_dump_json(exclude_none=True).decode('utf-8') + '\n')
            except Exception as e:
                print(f"Error writing JSON log: {e}")

            # Write human-readable log (manager/demo-friendly formatting)
            try:
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(human_log + '\n')
            except Exception as e:
                print(f"Error writing human log: {e}")

            # Write detailed audit report
            safe_timestamp = timestamp.replace(':', '-').replace('.', '-')
            report_filename = self.reports_dir / f"audit_report_{report.agent}_{safe_timestamp}.json"

            try:
                with open(report_filename, 'w', encoding='utf-8') as f:
                    # Convert to dict for JSON serialization
                    report_dict = report.model_dump()
                    # Ensure datetime is serialized properly
                    if 'audit_timestamp' in report_dict and report_dict['audit_timestamp']:
                        report_dict['audit_timestamp'] = report_dict['audit_timestamp'].isoformat()
                    json.dump(report_dict, f, indent=2, ensure_ascii=False)

                # Log successful report creation
                success_log = f"{timestamp} [STEP] Report saved - {report_filename.name}"
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(success_log + '\n')

            except Exception as e:
                error_log = f"{timestamp} [ERROR] Failed to save report: {e}"
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(error_log + '\n')
                print(f"Error writing audit report: {e}")

        except Exception as e:
            # Fallback logging if structured logging fails
            fallback_log = f"{timestamp} [ERROR] Logging failed for {report.agent}: {e}"
            try:
                with open(self.human_log_file, 'a', encoding='utf-8') as f:
                    f.write(fallback_log + '\n')
            except:
                print(fallback_log)





