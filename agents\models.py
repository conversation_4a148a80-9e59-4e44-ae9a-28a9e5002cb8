"""
Pydantic models for structured data validation in GuardrailAgent.

Following user preferences for using Pydantic models for structured validation
and parsing instead of manual dictionary access.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Literal
from pydantic import BaseModel, Field, validator
from enum import Enum


class AuditStatus(str, Enum):
    """Audit status enumeration"""
    PASSED = "PASSED"
    WARNING = "WARNING" 
    CRITICAL = "CRITICAL"


class EscalationStatus(str, Enum):
    """Escalation status enumeration"""
    RECOMMENDED = "RECOMMENDED"
    NOT_NEEDED = "NOT_NEEDED"


class AgentMetadata(BaseModel):
    """Standard metadata for agent outputs"""
    agent_type: str = Field(..., description="Type of agent that generated the output")
    agent_version: str = Field(..., description="Version of the agent")
    processing_time_ms: int = Field(..., ge=0, description="Processing time in milliseconds")
    task_id: str = Field(..., description="Unique task identifier")
    timestamp: str = Field(..., description="ISO8601 timestamp")


class ValidationIssue(BaseModel):
    """Represents a validation issue found in data"""
    row: Optional[int] = Field(None, description="Row number where issue occurred")
    column: str = Field(..., description="Column name where issue occurred")
    issue: str = Field(..., description="Description of the issue")
    severity: Optional[str] = Field(None, description="Severity level of the issue")


class ColumnValidationResult(BaseModel):
    """Result of validating a single column"""
    status: str = Field(..., description="Validation status (PASS/FAIL)")
    issues: Optional[List[str]] = Field(default_factory=list, description="List of issues found")
    expected_type: Optional[str] = Field(None, description="Expected data type")
    actual_type: Optional[str] = Field(None, description="Actual data type found")


class FileValidationOutput(BaseModel):
    """Expected output format from FileValidationAgent"""
    success: bool = Field(..., description="Whether validation was successful")
    message: str = Field(..., description="Human-readable validation message")
    validation_details: Dict[str, Any] = Field(..., description="Detailed validation results")
    verdict: str = Field(..., description="Overall validation verdict")
    logs: Optional[Dict[str, str]] = Field(default_factory=dict, description="Log file paths")
    
    # Optional metadata if present
    metadata: Optional[AgentMetadata] = Field(None, description="Agent metadata")


class DataValidationOutput(BaseModel):
    """Expected output format from DataValidationAgent"""
    success: bool = Field(..., description="Whether validation was successful")
    message: str = Field(..., description="Human-readable validation message")
    validation_results: List[Dict[str, Any]] = Field(..., description="Detailed validation results")
    override_rate: float = Field(..., ge=0.0, le=1.0, description="Rate of overrides applied")
    override_justifications: List[str] = Field(default_factory=list, description="Justifications for overrides")
    verdict: str = Field(..., description="Overall validation verdict")
    
    # Optional metadata if present
    metadata: Optional[AgentMetadata] = Field(None, description="Agent metadata")


class AuditFinding(BaseModel):
    """Represents a single audit finding"""
    category: str = Field(..., description="Category of the finding (schema, hallucination, threshold, etc.)")
    severity: str = Field(..., description="Severity level (INFO, WARNING, CRITICAL)")
    message: str = Field(..., description="Human-readable finding description")
    details: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional details")


class AuditReport(BaseModel):
    """Complete audit report structure"""
    agent: str = Field(..., description="Name of the agent being audited")
    phase: str = Field(..., description="Audit phase identifier")
    status: AuditStatus = Field(..., description="Overall audit status")
    findings: List[str] = Field(default_factory=list, description="List of audit findings")
    escalation: EscalationStatus = Field(..., description="Escalation recommendation")
    
    # Additional metadata
    audit_timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow, description="When audit was performed")
    processing_time_ms: Optional[int] = Field(None, ge=0, description="Time taken for audit")
    
    class Config:
        use_enum_values = True


class AuditLogEntry(BaseModel):
    """Structured log entry for audit events"""
    timestamp: str = Field(..., description="ISO8601 timestamp")
    type: str = Field(..., description="Log entry type")
    agent: str = Field(..., description="Agent being audited")
    phase: str = Field(..., description="Audit phase")
    status: str = Field(..., description="Audit status")
    findings_count: int = Field(..., ge=0, description="Number of findings")
    escalation: str = Field(..., description="Escalation status")
    
    # Optional additional fields
    processing_time_ms: Optional[int] = Field(None, ge=0, description="Processing time")
    error_message: Optional[str] = Field(None, description="Error message if any")


class AgentProfile(BaseModel):
    """Configuration profile for an agent"""
    max_processing_time_ms: int = Field(..., gt=0, description="Maximum allowed processing time")
    capabilities: List[str] = Field(..., description="List of agent capabilities")
    limitations: List[str] = Field(..., description="List of agent limitations")
    expected_output_structure: Dict[str, Any] = Field(..., description="Expected output schema")
    
    # Agent-specific thresholds
    min_coverage: Optional[float] = Field(None, ge=0.0, le=1.0, description="Minimum coverage required")
    max_override_rate: Optional[float] = Field(None, ge=0.0, le=1.0, description="Maximum override rate allowed")


class ThresholdConfig(BaseModel):
    """Threshold configuration for auditing"""
    escalation_thresholds: Dict[str, int] = Field(..., description="Escalation threshold values")
    performance_thresholds: Dict[str, Dict[str, Union[int, float]]] = Field(..., description="Performance thresholds by agent")
    schema_thresholds: Dict[str, int] = Field(..., description="Schema validation thresholds")
    business_logic_thresholds: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Business logic thresholds")


class AIAnalysisResult(BaseModel):
    """Result from AI analysis of agent output"""
    findings: List[str] = Field(default_factory=list, description="List of findings from AI analysis")
    status: AuditStatus = Field(..., description="Overall status determined by AI")
    escalation: EscalationStatus = Field(..., description="Escalation recommendation from AI")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="Confidence score of analysis")
    reasoning: Optional[str] = Field(None, description="AI reasoning for the decision")


class GuardrailConfig(BaseModel):
    """Complete configuration for GuardrailAgent"""
    agent_profiles: Dict[str, AgentProfile] = Field(..., description="Profiles for each agent type")
    thresholds: ThresholdConfig = Field(..., description="Threshold configurations")
    
    # AI configuration
    model: str = Field(default="gpt-4o-mini", description="AI model to use")
    temperature: float = Field(default=0.1, ge=0.0, le=2.0, description="AI temperature setting")
    max_retries: int = Field(default=3, ge=1, description="Maximum retry attempts for AI calls")
    timeout_seconds: int = Field(default=30, gt=0, description="Timeout for AI calls")
