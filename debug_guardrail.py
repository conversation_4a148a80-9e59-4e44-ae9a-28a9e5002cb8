#!/usr/bin/env python3
"""
Test script for the improved AI-powered GuardrailAgent
"""

import json
import os
from pathlib import Path
from agents.guardrail_agent import GuardrailAgent

def test_ai_powered_guardrail():
    """Test the completely AI-powered GuardrailAgent"""
    print("="*60)
    print("TESTING: AI-Powered GuardrailAgent (No Hardcoding)")
    print("="*60)

    # Check environment
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ OPENAI_API_KEY not found in environment")
        return

    try:
        print("🔄 Initializing AI-powered GuardrailAgent...")
        guardrail = GuardrailAgent()
        print("✅ GuardrailAgent initialized successfully")

        # Test with actual FileValidationAgent output
        print("\n🔄 Testing with FileValidationAgent output...")
        with open('test_data/file_validation_output.json', 'r') as f:
            file_validation_output = json.load(f)

        result1 = guardrail.audit_agent_output(
            agent_name="FileValidationAgent",
            agent_phase="Audit Phase 1",
            agent_output=file_validation_output
        )

        print(f"✅ FileValidationAgent audit completed")
        print(f"   Status: {result1.get('status')}")
        print(f"   Findings: {len(result1.get('findings', []))}")
        print(f"   Escalation: {result1.get('escalation')}")

        # Test with actual DataValidationAgent output
        print("\n🔄 Testing with DataValidationAgent output...")
        with open('test_data/data_validation_output.json', 'r') as f:
            data_validation_output = json.load(f)

        result2 = guardrail.audit_agent_output(
            agent_name="DataValidationAgent",
            agent_phase="Audit Phase 2",
            agent_output=data_validation_output
        )

        print(f"✅ DataValidationAgent audit completed")
        print(f"   Status: {result2.get('status')}")
        print(f"   Findings: {len(result2.get('findings', []))}")
        print(f"   Escalation: {result2.get('escalation')}")

        # Check data storage
        print("\n🔄 Checking data storage...")

        audit_logs = Path('data/audit_logs/audit_logs.jsonl')
        human_logs = Path('data/audit_logs/audit_human_readable.log')
        reports_dir = Path('data/reports')

        print(f"   JSON logs: {'✅' if audit_logs.exists() else '❌'} ({audit_logs.stat().st_size if audit_logs.exists() else 0} bytes)")
        print(f"   Human logs: {'✅' if human_logs.exists() else '❌'} ({human_logs.stat().st_size if human_logs.exists() else 0} bytes)")

        if reports_dir.exists():
            reports = list(reports_dir.glob('*.json'))
            print(f"   Reports: ✅ {len(reports)} files created")
            for report in reports[-2:]:  # Show last 2 reports
                print(f"     - {report.name}")
        else:
            print(f"   Reports: ❌ No reports directory")

        # Show sample findings
        print("\n📋 Sample Findings:")
        for i, finding in enumerate(result1.get('findings', [])[:3], 1):
            print(f"   {i}. {finding}")

        print("\n🎉 All tests completed successfully!")
        print("🤖 GuardrailAgent is fully AI-powered with no hardcoding")

    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ai_powered_guardrail()
