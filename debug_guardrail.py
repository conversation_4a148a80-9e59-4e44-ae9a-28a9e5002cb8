#!/usr/bin/env python3
"""
Debug script to test GuardrailAgent data storage issues
"""

import json
import os
from pathlib import Path
from agents.guardrail_agent import GuardrailAgent

def test_basic_functionality():
    """Test basic GuardrailAgent functionality and data storage"""
    print("="*60)
    print("DEBUG: Testing GuardrailAgent Basic Functionality")
    print("="*60)
    
    # Check if directories exist
    print(f"Data directory exists: {Path('data').exists()}")
    print(f"Audit logs directory exists: {Path('data/audit_logs').exists()}")
    print(f"Reports directory exists: {Path('data/reports').exists()}")
    
    # Check if config files exist
    print(f"Agent profiles exist: {Path('config/agent_profiles.yaml').exists()}")
    print(f"Thresholds exist: {Path('config/thresholds.yaml').exists()}")
    
    # Check if test data exists
    print(f"File validation output exists: {Path('test_data/file_validation_output.json').exists()}")
    
    # Load test data
    try:
        with open('test_data/file_validation_output.json', 'r') as f:
            test_data = json.load(f)
        print(f"Test data loaded successfully: {len(str(test_data))} characters")
        print(f"Test data keys: {list(test_data.keys())}")
    except Exception as e:
        print(f"Error loading test data: {e}")
        return
    
    # Try to initialize GuardrailAgent
    try:
        print("\nInitializing GuardrailAgent...")
        guardrail = GuardrailAgent()
        print("GuardrailAgent initialized successfully")
        
        # Check if configurations loaded
        print(f"Agent profiles loaded: {len(guardrail.agent_profiles)} agents")
        print(f"Thresholds loaded: {len(guardrail.thresholds)} threshold categories")
        
    except Exception as e:
        print(f"Error initializing GuardrailAgent: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Try a simple audit without AI call first
    print("\nTesting audit method...")
    try:
        # Create a simple test output that matches expected format
        simple_test_output = {
            "metadata": {
                "agent_type": "FileValidationAgent",
                "agent_version": "1.0.0",
                "processing_time_ms": 1500,
                "task_id": "test-123",
                "timestamp": "2024-01-01T12:00:00Z"
            },
            "validation_coverage": 0.99,
            "valid_rows": 100,
            "invalid_rows": 1,
            "column_validation": {
                "UNIQUE_ID": {"status": "PASS"},
                "NAV": {"status": "FAIL", "issues": ["null values found"]}
            }
        }
        
        print("Calling audit_agent_output with simple test data...")
        result = guardrail.audit_agent_output(
            agent_name="FileValidationAgent",
            agent_phase="Audit Phase 1", 
            agent_output=simple_test_output
        )
        
        print(f"Audit completed successfully!")
        print(f"Result status: {result.get('status')}")
        print(f"Findings count: {len(result.get('findings', []))}")
        print(f"Escalation: {result.get('escalation')}")
        
        # Check if files were created
        print("\nChecking if log files were created...")
        audit_log_file = Path('data/audit_logs/audit_logs.jsonl')
        human_log_file = Path('data/audit_logs/audit_human_readable.log')
        
        print(f"JSON log file exists: {audit_log_file.exists()}")
        if audit_log_file.exists():
            print(f"JSON log file size: {audit_log_file.stat().st_size} bytes")
            
        print(f"Human log file exists: {human_log_file.exists()}")
        if human_log_file.exists():
            print(f"Human log file size: {human_log_file.stat().st_size} bytes")
            
        # Check reports directory
        reports_dir = Path('data/reports')
        if reports_dir.exists():
            report_files = list(reports_dir.glob('*.json'))
            print(f"Report files created: {len(report_files)}")
            for report_file in report_files:
                print(f"  - {report_file.name} ({report_file.stat().st_size} bytes)")
        
    except Exception as e:
        print(f"Error during audit: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_functionality()
