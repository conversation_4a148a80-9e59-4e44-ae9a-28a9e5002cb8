2025-07-14T12:17:17.567427+00:00 [START] Audit starting - FileValidationAgent Audit Phase 1
2025-07-14T12:17:25.749112+00:00 [WARNING] AI analysis attempt 1 failed: 2 validation errors for AIAnalysisResult
findings.0
  Input should be a valid string [type=string_type, input_value={'field': 'required_const... validity of the data."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.1
  Input should be a valid string [type=string_type, input_value={'field': 'verdict', 'iss...he necessary criteria."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-07-14T12:17:30.393600+00:00 [WARNING] AI analysis attempt 2 failed: 2 validation errors for AIAnalysisResult
findings.0
  Input should be a valid string [type=string_type, input_value={'field': 'required_const... validity of the data."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.1
  Input should be a valid string [type=string_type, input_value={'field': 'verdict', 'iss...or further processing."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-07-14T12:17:34.640932+00:00 [WARNING] AI analysis attempt 3 failed: 2 validation errors for AIAnalysisResult
findings.0
  Input should be a valid string [type=string_type, input_value={'field': 'required_const...contains a null value."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.1
  Input should be a valid string [type=string_type, input_value={'field': 'verdict', 'iss...cal schema violations."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-07-14T12:17:34.641611+00:00 [ERROR] Logging failed for FileValidationAgent: 'str' object has no attribute 'value'
2025-07-14T12:17:35.088721+00:00 [START] Audit starting - DataValidationAgent Audit Phase 2
2025-07-14T12:17:50.518539+00:00 [WARNING] AI analysis attempt 1 failed: 9 validation errors for AIAnalysisResult
findings.0
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...mpleteness validation.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.1
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ess validation rule 8.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.2
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ess validation rule 8.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.3
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ated in rows: [2, 32]."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.4
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ted in rows: [33, 91]."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.5
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ted in rows: [41, 97]."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.6
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...icated in another row."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.7
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ted in rows: [44, 64]."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.8
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...fic validation rule 6."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-07-14T12:17:58.203955+00:00 [WARNING] AI analysis attempt 2 failed: 5 validation errors for AIAnalysisResult
findings.0
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...mpleteness validation.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.1
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ess validation rule 8.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.2
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ess validation rule 8.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.3
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...uniqueness constraint.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.4
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ng completeness rules.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-07-14T12:18:08.582856+00:00 [WARNING] AI analysis attempt 3 failed: 6 validation errors for AIAnalysisResult
findings.0
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...uniqueness constraint."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.1
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res... field to be not null.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.2
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res... field to be not null.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.3
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res... field to be not null.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.4
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...he validation results.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
findings.5
  Input should be a valid string [type=string_type, input_value={'field': 'validation_res...ata type requirements.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-07-14T12:18:08.583413+00:00 [ERROR] Logging failed for DataValidationAgent: 'str' object has no attribute 'value'
